import { useState, useRef, useEffect } from 'react';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import {
  ArrowLeft,
  Save,
  FileText,
  Plus,
  MoreHorizontal,
  Edit3,
  Trash2,
  Send,
  Bot,
  User,
  ChevronDown,
  ChevronRight,
  MessageSquare,
  Bold,
  Italic,
  Underline,
  List,
  ListOrdered,
  Quote,
  Link,
  Image,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Type,
  Palette,
  BookOpen,
  Database,
  Table,
  BarChart3,
  Tag
} from 'lucide-react';

interface Section {
  id: string;
  title: string;
  content: string;
  level: number;
  children?: Section[];
  generatedContent?: GeneratedContent[];
  isGenerated?: boolean;
}

interface GeneratedContent {
  id: string;
  content: string;
  sourceCards: ReferenceCard[];
  timestamp: Date;
  type: 'ai_generated' | 'user_written';
}

interface ReferenceCard {
  id: string;
  title: string;
  content: string;
  source: string;
  type: 'text' | 'table' | 'chart' | 'formula';
  relevanceScore?: number;
  tags: string[];
  category: string;
  summary: string;
  sourcePageNumber: number;
  confidence: number;
  aiProcessed: boolean;
}

interface ChatMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  sourceCards?: ReferenceCard[];
}

const DocumentEditor = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { documentId } = useParams();
  const chatEndRef = useRef<HTMLDivElement>(null);

  // 从路由状态获取项目信息
  const { projectId, templateId, mode } = location.state || {};
  
  // 根据模板ID获取文档标题
  const getDocumentTitle = () => {
    if (templateId) {
      const templateTitles: { [key: string]: string } = {
        '1': '建设项目环境影响报告书',
        '2': '建设项目环境影响报告表',
        '3': '环境影响登记表',
        '4': '水土保持方案报告书',
        '5': '安全评价报告',
        '6': '职业病危害预评价',
        '7': '家居制造业环评报告',
        '8': '医院建设项目环评',
        '9': '塑料制品安全评价'
      };
      return templateTitles[templateId] || '新建文档';
    }
    return mode === 'edit' ? '编辑文档' : '新建文档';
  };

  // 文档状态
  const [documentTitle, setDocumentTitle] = useState(getDocumentTitle());
  const [sections, setSections] = useState<Section[]>([
    {
      id: '1',
      title: '1. 项目概述',
      content: '本项目位于...',
      level: 1,
      children: [
        {
          id: '1-1',
          title: '1.1 项目基本信息',
          content: '项目名称：\n建设单位：\n建设地点：',
          level: 2
        },
        {
          id: '1-2',
          title: '1.2 项目建设内容',
          content: '建设规模：\n主要建设内容：',
          level: 2
        }
      ]
    },
    {
      id: '2',
      title: '2. 环境现状调查',
      content: '根据现场调查和监测数据...',
      level: 1,
      children: [
        {
          id: '2-1',
          title: '2.1 自然环境',
          content: '地理位置：\n地形地貌：\n气候条件：',
          level: 2
        }
      ]
    },
    {
      id: '3',
      title: '3. 环境影响预测',
      content: '基于工程分析结果...',
      level: 1
    }
  ]);
  
  const [activeSection, setActiveSection] = useState<string>('1');
  const [expandedSections, setExpandedSections] = useState<string[]>(['1', '2']);
  
  // 模拟参考资料卡片数据
  const [referenceCards] = useState<ReferenceCard[]>([
    {
      id: 'card_1',
      title: '环境影响评价技术导则',
      content: '建设项目环境影响评价应当遵循以下原则：依法评价、科学评价、突出重点、注重实效...',
      source: '环评技术导则.pdf',
      type: 'text',
      relevanceScore: 0.95,
      tags: ['环评', '技术导则', '评价原则'],
      category: '技术规范',
      summary: '环境影响评价的基本原则和技术要求',
      sourcePageNumber: 1,
      confidence: 0.95,
      aiProcessed: true
    },
    {
      id: 'card_2',
      title: '项目基本信息表',
      content: '项目名称：某工业园区建设项目\n建设单位：XX环保科技有限公司\n建设地点：XX市XX区...',
      source: '项目申报材料.docx',
      type: 'text',
      relevanceScore: 0.88,
      tags: ['项目信息', '基本资料', '申报材料'],
      category: '项目资料',
      summary: '项目的基本信息和建设单位情况',
      sourcePageNumber: 3,
      confidence: 0.88,
      aiProcessed: true
    },
    {
      id: 'card_3',
      title: '环境质量现状监测数据',
      content: 'PM2.5: 35μg/m³\nPM10: 68μg/m³\nSO2: 15μg/m³\nNO2: 42μg/m³...',
      source: '监测报告.xlsx',
      type: 'table',
      relevanceScore: 0.82,
      tags: ['监测数据', '环境质量', '大气污染'],
      category: '监测数据',
      summary: '项目区域环境质量现状监测结果',
      sourcePageNumber: 15,
      confidence: 0.82,
      aiProcessed: true
    },
    {
      id: 'card_4',
      title: '项目地理位置图',
      content: '项目位于XX市XX区，东临XX路，西接XX河，南靠XX工业园...',
      source: '地理位置图.jpg',
      type: 'chart',
      relevanceScore: 0.76,
      tags: ['地理位置', '项目选址', '周边环境'],
      category: '地理信息',
      summary: '项目地理位置和周边环境情况',
      sourcePageNumber: 5,
      confidence: 0.76,
      aiProcessed: true
    }
  ]);

  // 聊天状态
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([
    {
      id: '1',
      type: 'assistant',
      content: '你好！我是你的写作助手。我已经为您生成了初始内容，以下是生成当前章节时使用的参考资料：',
      timestamp: new Date(),
      sourceCards: referenceCards.filter(card => card.relevanceScore && card.relevanceScore > 0.7)
    },
    {
      id: '2',
      type: 'assistant',
      content: '我可以帮助你：\n\n• 基于参考资料生成章节内容\n• 优化文字表达\n• 查找相关资料卡片\n• 提供专业建议\n\n有什么需要帮助的吗？',
      timestamp: new Date()
    }
  ]);
  const [chatInput, setChatInput] = useState('');
  const [isTyping, setIsTyping] = useState(false);

  // 新增状态
  const [isGeneratingContent, setIsGeneratingContent] = useState(false);
  const [showReferenceCards, setShowReferenceCards] = useState(false);
  const [selectedCards, setSelectedCards] = useState<string[]>([]);

  // 编辑状态
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [editingTitleValue, setEditingTitleValue] = useState(documentTitle);

  // 右侧边栏tab状态
  const [rightSidebarTab, setRightSidebarTab] = useState<'references' | 'chat'>('chat');

  // 编辑器工具栏状态
  const [editorToolbar, setEditorToolbar] = useState({
    bold: false,
    italic: false,
    underline: false,
    fontSize: '14',
    textAlign: 'left' as 'left' | 'center' | 'right'
  });

  // 获取当前激活的章节
  const getCurrentSection = (): Section | undefined => {
    const findSection = (sections: Section[]): Section | undefined => {
      for (const section of sections) {
        if (section.id === activeSection) return section;
        if (section.children) {
          const found = findSection(section.children);
          if (found) return found;
        }
      }
      return undefined;
    };
    return findSection(sections);
  };

  // 自动生成章节内容
  const generateSectionContent = async (sectionId: string) => {
    setIsGeneratingContent(true);

    // 模拟AI生成过程
    setTimeout(() => {
      const relevantCards = referenceCards.filter(card => card.relevanceScore && card.relevanceScore > 0.7);
      const generatedContent = generateContentBasedOnCards(sectionId, relevantCards);

      // 更新章节内容
      const updateSection = (sections: Section[]): Section[] => {
        return sections.map(section => {
          if (section.id === sectionId) {
            return {
              ...section,
              content: generatedContent.content,
              isGenerated: true,
              generatedContent: [generatedContent]
            };
          }
          if (section.children) {
            return { ...section, children: updateSection(section.children) };
          }
          return section;
        });
      };

      setSections(updateSection(sections));
      setIsGeneratingContent(false);

      // 添加生成消息到聊天
      const assistantMessage: ChatMessage = {
        id: Date.now().toString(),
        type: 'assistant',
        content: `已为"${getCurrentSection()?.title}"章节生成内容，参考了${relevantCards.length}个相关资料。`,
        timestamp: new Date(),
        sourceCards: relevantCards
      };
      setChatMessages(prev => [...prev, assistantMessage]);
    }, 2000);
  };

  // 基于卡片生成内容
  const generateContentBasedOnCards = (sectionId: string, cards: ReferenceCard[]): GeneratedContent => {
    const sectionTemplates: { [key: string]: string } = {
      '1': `根据项目基本信息，本项目为${cards.find(c => c.category === '项目资料')?.content.split('\n')[0] || '建设项目'}。

项目建设的主要目的是满足区域发展需求，提升环境保护水平。根据相关技术导则要求，本次环境影响评价将严格按照国家相关法规和标准进行。

参考资料显示：${cards.map(c => c.title).join('、')}等文件为本次评价提供了重要依据。`,

      '2': `根据现场调查和监测数据分析，项目所在区域环境质量现状如下：

大气环境：${cards.find(c => c.type === 'table')?.content || '各项指标均符合环境质量标准要求'}

地理位置：${cards.find(c => c.type === 'chart')?.content || '项目地理位置优越，周边环境良好'}

本次现状调查严格按照相关技术规范执行，数据真实可靠。`,

      '3': `基于工程分析和环境现状调查结果，预测项目建设和运营期间的环境影响：

1. 大气环境影响预测
根据监测数据和技术导则要求，项目排放的污染物对周边环境影响较小。

2. 水环境影响预测
项目废水经处理后达标排放，对水环境影响可接受。

3. 声环境影响预测
项目运营期噪声经治理后能够满足相关标准要求。`
    };

    return {
      id: `gen_${Date.now()}`,
      content: sectionTemplates[sectionId] || `基于参考资料生成的${getCurrentSection()?.title}内容...`,
      sourceCards: cards,
      timestamp: new Date(),
      type: 'ai_generated'
    };
  };

  // 更新章节内容
  const updateSectionContent = (content: string) => {
    const updateSection = (sections: Section[]): Section[] => {
      return sections.map(section => {
        if (section.id === activeSection) {
          return { ...section, content };
        }
        if (section.children) {
          return { ...section, children: updateSection(section.children) };
        }
        return section;
      });
    };
    setSections(updateSection(sections));
  };

  // 切换章节展开状态
  const toggleSectionExpanded = (sectionId: string) => {
    setExpandedSections(prev =>
      prev.includes(sectionId)
        ? prev.filter(id => id !== sectionId)
        : [...prev, sectionId]
    );
  };

  // 添加新章节
  const addNewSection = () => {
    const newSection: Section = {
      id: Date.now().toString(),
      title: `${sections.length + 1}. 新章节`,
      content: '',
      level: 1
    };
    setSections(prev => [...prev, newSection]);
    setActiveSection(newSection.id);
  };

  // 添加子章节
  const addSubSection = (parentId: string) => {
    const updateSections = (sections: Section[]): Section[] => {
      return sections.map(section => {
        if (section.id === parentId) {
          const newSubSection: Section = {
            id: `${parentId}-${Date.now()}`,
            title: `${section.title.split('.')[0]}.${(section.children?.length || 0) + 1} 新子章节`,
            content: '',
            level: 2
          };
          return {
            ...section,
            children: [...(section.children || []), newSubSection]
          };
        }
        if (section.children) {
          return { ...section, children: updateSections(section.children) };
        }
        return section;
      });
    };
    setSections(updateSections(sections));
    setExpandedSections(prev => [...prev, parentId]);
  };

  // 编辑器工具栏功能
  const applyFormat = (format: string, value?: string) => {
    const textarea = document.querySelector('textarea') as HTMLTextAreaElement;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = textarea.value.substring(start, end);
    let newText = '';

    switch (format) {
      case 'bold':
        newText = `**${selectedText}**`;
        setEditorToolbar(prev => ({ ...prev, bold: !prev.bold }));
        break;
      case 'italic':
        newText = `*${selectedText}*`;
        setEditorToolbar(prev => ({ ...prev, italic: !prev.italic }));
        break;
      case 'underline':
        newText = `<u>${selectedText}</u>`;
        setEditorToolbar(prev => ({ ...prev, underline: !prev.underline }));
        break;
      case 'list':
        newText = selectedText.split('\n').map(line => line.trim() ? `• ${line}` : line).join('\n');
        break;
      case 'orderedList':
        newText = selectedText.split('\n').map((line, index) => line.trim() ? `${index + 1}. ${line}` : line).join('\n');
        break;
      case 'quote':
        newText = selectedText.split('\n').map(line => line.trim() ? `> ${line}` : line).join('\n');
        break;
      case 'link':
        const url = prompt('请输入链接地址:');
        if (url) {
          newText = `[${selectedText || '链接文本'}](${url})`;
        } else {
          return;
        }
        break;
      case 'heading':
        newText = `## ${selectedText}`;
        break;
      default:
        return;
    }

    const newContent = textarea.value.substring(0, start) + newText + textarea.value.substring(end);
    updateSectionContent(newContent);

    // 重新设置光标位置
    setTimeout(() => {
      textarea.focus();
      textarea.setSelectionRange(start + newText.length, start + newText.length);
    }, 0);
  };

  // 插入内容
  const insertContent = (content: string) => {
    const textarea = document.querySelector('textarea') as HTMLTextAreaElement;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const currentContent = textarea.value;
    const newContent = currentContent.substring(0, start) + content + currentContent.substring(start);

    updateSectionContent(newContent);

    setTimeout(() => {
      textarea.focus();
      textarea.setSelectionRange(start + content.length, start + content.length);
    }, 0);
  };

  // 发送聊天消息
  const sendMessage = async (customMessage?: string) => {
    const messageToSend = customMessage || chatInput;
    if (!messageToSend.trim()) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: messageToSend,
      timestamp: new Date()
    };

    setChatMessages(prev => [...prev, userMessage]);
    setChatInput('');
    setIsTyping(true);

    // 模拟AI回复
    setTimeout(() => {
      const aiResponse = generateAIResponse(messageToSend);
      const assistantMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: aiResponse.content,
        timestamp: new Date(),
        sourceCards: aiResponse.sourceCards
      };
      setChatMessages(prev => [...prev, assistantMessage]);
      setIsTyping(false);
    }, 1500);
  };

  // 查找相关卡片
  const findRelevantCards = (query: string): ReferenceCard[] => {
    const keywords = query.toLowerCase().split(' ');
    return referenceCards.filter(card => {
      const searchText = `${card.title} ${card.content}`.toLowerCase();
      return keywords.some(keyword => searchText.includes(keyword));
    }).sort((a, b) => (b.relevanceScore || 0) - (a.relevanceScore || 0));
  };

  // 生成AI回复
  const generateAIResponse = (input: string): { content: string; sourceCards?: ReferenceCard[] } => {
    const query = input.toLowerCase();

    // 检查是否是查找资料的请求
    if (query.includes('查找') || query.includes('搜索') || query.includes('资料') || query.includes('参考')) {
      const relevantCards = findRelevantCards(input);
      if (relevantCards.length > 0) {
        return {
          content: `我找到了${relevantCards.length}个相关资料：\n\n${relevantCards.slice(0, 3).map(card =>
            `• ${card.title}\n  来源：${card.source}\n  相关度：${Math.round((card.relevanceScore || 0) * 100)}%`
          ).join('\n\n')}${relevantCards.length > 3 ? `\n\n还有${relevantCards.length - 3}个相关资料...` : ''}`,
          sourceCards: relevantCards
        };
      }
    }

    // 检查是否是生成内容的请求
    if (query.includes('生成') || query.includes('写') || query.includes('内容')) {
      const relevantCards = findRelevantCards(input);
      return {
        content: `我可以基于以下参考资料为你生成内容：\n\n${relevantCards.slice(0, 2).map(card =>
          `• ${card.title} (相关度: ${Math.round((card.relevanceScore || 0) * 100)}%)`
        ).join('\n')}\n\n请点击"基于资料生成"按钮，或者告诉我具体需要什么样的内容。`,
        sourceCards: relevantCards
      };
    }

    // 默认回复
    const responses = [
      '我建议在这个章节中添加更多具体的数据和案例来支撑你的观点。',
      '这个表述很好，不过可以考虑使用更专业的术语来提升文档的权威性。',
      '建议在此处添加相关的法规依据和标准要求。',
      '内容结构清晰，建议补充一些图表来更直观地展示信息。',
      '这部分内容可以进一步细化，添加具体的实施步骤和时间安排。'
    ];
    return { content: responses[Math.floor(Math.random() * responses.length)] };
  };

  // 保存文档
  const saveDocument = () => {
    console.log('保存文档:', {
      documentId,
      documentTitle,
      sections,
      projectId,
      templateId
    });

    // 模拟保存过程
    const saveData = {
      id: documentId,
      title: documentTitle,
      content: sections,
      projectId: projectId,
      templateId: templateId,
      lastModified: new Date().toISOString(),
      wordCount: sections.reduce((total, section) => {
        const sectionWords = section.content.length + (section.children?.reduce((subTotal, child) => subTotal + child.content.length, 0) || 0);
        return total + sectionWords;
      }, 0)
    };

    // 这里应该调用API保存文档
    console.log('保存的文档数据:', saveData);

    // 显示保存成功提示
    const saveButton = document.querySelector('.btn-primary');
    if (saveButton) {
      const originalText = saveButton.innerHTML;
      saveButton.innerHTML = '<svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>已保存';
      setTimeout(() => {
        saveButton.innerHTML = originalText;
      }, 2000);
    }
  };

  // 滚动到聊天底部
  useEffect(() => {
    chatEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [chatMessages]);

  // 用户进入文档时自动生成内容
  useEffect(() => {
    const hasEmptyContent = sections.some(section =>
      !section.content.trim() ||
      (section.children && section.children.some(child => !child.content.trim()))
    );

    if (hasEmptyContent && !isGeneratingContent && mode !== 'edit') {
      // 延迟一下，让用户看到界面加载
      setTimeout(() => {
        const welcomeMessage: ChatMessage = {
          id: Date.now().toString(),
          type: 'assistant',
          content: '检测到新文档，我来帮你生成初始内容。正在分析相关参考资料...',
          timestamp: new Date()
        };
        setChatMessages(prev => [...prev, welcomeMessage]);

        // 为空的章节生成内容
        sections.forEach(section => {
          if (!section.content.trim()) {
            generateSectionContent(section.id);
          }
          if (section.children) {
            section.children.forEach(child => {
              if (!child.content.trim()) {
                generateSectionContent(child.id);
              }
            });
          }
        });
      }, 1000);
    }
  }, [documentId, mode, sections, isGeneratingContent]); // 只在文档ID或模式改变时触发

  // 键盘快捷键
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // 只在textarea聚焦时处理格式化快捷键
      const isTextareaFocused = document.activeElement?.tagName === 'TEXTAREA';

      // Ctrl+S 保存文档
      if ((e.ctrlKey || e.metaKey) && e.key === 's') {
        e.preventDefault();
        saveDocument();
      }
      // Ctrl+E 编辑标题
      if ((e.ctrlKey || e.metaKey) && e.key === 'e' && !isEditingTitle) {
        e.preventDefault();
        setEditingTitleValue(documentTitle);
        setIsEditingTitle(true);
      }

      // 编辑器格式化快捷键
      if (isTextareaFocused && currentSection) {
        if ((e.ctrlKey || e.metaKey) && e.key === 'b') {
          e.preventDefault();
          applyFormat('bold');
        }
        if ((e.ctrlKey || e.metaKey) && e.key === 'i') {
          e.preventDefault();
          applyFormat('italic');
        }
        if ((e.ctrlKey || e.metaKey) && e.key === 'u') {
          e.preventDefault();
          applyFormat('underline');
        }
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
          e.preventDefault();
          applyFormat('link');
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [documentTitle, isEditingTitle, activeSection]);

  const currentSection = getCurrentSection();

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      {/* 顶部工具栏 */}
      <div className="bg-white border-b border-gray-200 px-6 py-4 flex items-center justify-between shadow-sm">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => {
              if (projectId) {
                navigate(`/my-workspace/private/${projectId}`);
              } else {
                navigate('/my-workspace/private');
              }
            }}
            className="text-gray-500 hover:text-gray-700 transition-colors"
            title="返回项目"
          >
            <ArrowLeft className="h-5 w-5" />
          </button>

          {isEditingTitle ? (
            <input
              type="text"
              value={editingTitleValue}
              onChange={(e) => setEditingTitleValue(e.target.value)}
              onBlur={() => {
                if (editingTitleValue.trim()) {
                  setDocumentTitle(editingTitleValue.trim());
                } else {
                  setEditingTitleValue(documentTitle);
                }
                setIsEditingTitle(false);
              }}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  if (editingTitleValue.trim()) {
                    setDocumentTitle(editingTitleValue.trim());
                  } else {
                    setEditingTitleValue(documentTitle);
                  }
                  setIsEditingTitle(false);
                } else if (e.key === 'Escape') {
                  setEditingTitleValue(documentTitle);
                  setIsEditingTitle(false);
                }
              }}
              className="text-lg font-semibold text-gray-900 bg-transparent border-b-2 border-blue-500 focus:outline-none min-w-[300px] px-1"
              autoFocus
              placeholder="输入文档标题..."
            />
          ) : (
            <div className="flex items-center space-x-2">
              <h1
                className="text-lg font-semibold text-gray-900 cursor-pointer hover:text-blue-600 transition-colors px-1 py-1 rounded hover:bg-blue-50"
                onClick={() => {
                  setEditingTitleValue(documentTitle);
                  setIsEditingTitle(true);
                }}
                title="点击编辑标题"
              >
                {documentTitle}
              </h1>
              <Edit3 className="h-4 w-4 text-gray-400" />
            </div>
          )}
        </div>

        <div className="flex items-center space-x-3">
          <button
            onClick={saveDocument}
            className="btn-primary flex items-center"
            title="保存文档 (Ctrl+S)"
          >
            <Save className="h-4 w-4 mr-2" />
            保存文档
          </button>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 flex overflow-hidden">
        {/* 左侧文档目录 */}
        <div className="w-56 bg-white border-r border-gray-200 flex flex-col">
          <div className="p-3 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium text-gray-900">文档目录</h3>
              <button
                onClick={addNewSection}
                className="text-gray-400 hover:text-gray-600"
                title="添加新章节"
              >
                <Plus className="h-4 w-4" />
              </button>
            </div>
          </div>

          <div className="flex-1 overflow-y-auto p-2">
            {/* 目录树 */}
            {sections.map((section) => (
              <div key={section.id} className="mb-1">
                <div
                  className={`flex items-center px-2 py-2 rounded-lg cursor-pointer group ${
                    activeSection === section.id
                      ? 'bg-blue-50 text-blue-700'
                      : 'text-gray-700 hover:bg-gray-50'
                  }`}
                  onClick={() => setActiveSection(section.id)}
                >
                  {section.children && section.children.length > 0 && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleSectionExpanded(section.id);
                      }}
                      className="mr-1 p-0.5 rounded hover:bg-gray-200"
                    >
                      {expandedSections.includes(section.id) ? (
                        <ChevronDown className="h-3 w-3" />
                      ) : (
                        <ChevronRight className="h-3 w-3" />
                      )}
                    </button>
                  )}
                  <FileText className="h-3 w-3 mr-2 flex-shrink-0" />
                  <span className="text-xs font-medium truncate flex-1">
                    {section.title}
                  </span>
                  <button className="opacity-0 group-hover:opacity-100 p-1 rounded hover:bg-gray-200">
                    <MoreHorizontal className="h-3 w-3" />
                  </button>
                </div>

                {/* 子章节 */}
                {section.children && expandedSections.includes(section.id) && (
                  <div className="ml-3 mt-1">
                    {section.children.map((child) => (
                      <div
                        key={child.id}
                        className={`flex items-center px-2 py-1.5 rounded-lg cursor-pointer group ${
                          activeSection === child.id
                            ? 'bg-blue-50 text-blue-700'
                            : 'text-gray-600 hover:bg-gray-50'
                        }`}
                        onClick={() => setActiveSection(child.id)}
                      >
                        <div className="w-3 mr-1"></div>
                        <FileText className="h-3 w-3 mr-1 flex-shrink-0" />
                        <span className="text-xs truncate flex-1">
                          {child.title}
                        </span>
                        <button className="opacity-0 group-hover:opacity-100 p-0.5 rounded hover:bg-gray-200">
                          <MoreHorizontal className="h-3 w-3" />
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* 中间编辑区域 */}
        <div className="flex-1 flex flex-col">
          {/* 章节信息栏 */}
          <div className="bg-white border-b border-gray-200 px-6 py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <span>当前章节:</span>
                <span className="font-medium text-gray-900">
                  {currentSection?.title || '请选择章节'}
                </span>
              </div>

              {currentSection && (
                <button
                  onClick={() => {
                    // 保存当前章节
                    console.log('保存章节:', currentSection);
                    // 这里可以添加具体的保存逻辑

                    // 显示保存成功提示
                    const button = document.querySelector('.save-section-btn') as HTMLButtonElement;
                    if (button) {
                      const originalText = button.innerHTML;
                      button.innerHTML = '<svg class="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>已保存';
                      setTimeout(() => {
                        button.innerHTML = originalText;
                      }, 2000);
                    }
                  }}
                  className="save-section-btn flex items-center px-3 py-1.5 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                  title="保存当前章节"
                >
                  <Save className="h-3 w-3 mr-1" />
                  保存章节
                </button>
              )}
            </div>
          </div>

          {/* 编辑器工具栏 */}
          {currentSection && (
            <div className="bg-gray-50 border-b border-gray-200 px-6 py-2">
              <div className="flex items-center space-x-1">
                {/* 字体格式 */}
                <div className="flex items-center space-x-1 mr-4">
                  <button
                    onClick={() => applyFormat('bold')}
                    className={`p-2 rounded hover:bg-gray-200 transition-colors ${
                      editorToolbar.bold ? 'bg-gray-200 text-blue-600' : 'text-gray-600'
                    }`}
                    title="加粗 (Ctrl+B)"
                  >
                    <Bold className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => applyFormat('italic')}
                    className={`p-2 rounded hover:bg-gray-200 transition-colors ${
                      editorToolbar.italic ? 'bg-gray-200 text-blue-600' : 'text-gray-600'
                    }`}
                    title="斜体 (Ctrl+I)"
                  >
                    <Italic className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => applyFormat('underline')}
                    className={`p-2 rounded hover:bg-gray-200 transition-colors ${
                      editorToolbar.underline ? 'bg-gray-200 text-blue-600' : 'text-gray-600'
                    }`}
                    title="下划线 (Ctrl+U)"
                  >
                    <Underline className="h-4 w-4" />
                  </button>
                </div>

                {/* 分隔线 */}
                <div className="w-px h-6 bg-gray-300 mr-4"></div>

                {/* 列表和引用 */}
                <div className="flex items-center space-x-1 mr-4">
                  <button
                    onClick={() => applyFormat('list')}
                    className="p-2 rounded hover:bg-gray-200 text-gray-600 transition-colors"
                    title="无序列表"
                  >
                    <List className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => applyFormat('orderedList')}
                    className="p-2 rounded hover:bg-gray-200 text-gray-600 transition-colors"
                    title="有序列表"
                  >
                    <ListOrdered className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => applyFormat('quote')}
                    className="p-2 rounded hover:bg-gray-200 text-gray-600 transition-colors"
                    title="引用"
                  >
                    <Quote className="h-4 w-4" />
                  </button>
                </div>

                {/* 分隔线 */}
                <div className="w-px h-6 bg-gray-300 mr-4"></div>

                {/* 插入功能 */}
                <div className="flex items-center space-x-1 mr-4">
                  <button
                    onClick={() => applyFormat('link')}
                    className="p-2 rounded hover:bg-gray-200 text-gray-600 transition-colors"
                    title="插入链接"
                  >
                    <Link className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => insertContent('\n![图片描述](图片链接)\n')}
                    className="p-2 rounded hover:bg-gray-200 text-gray-600 transition-colors"
                    title="插入图片"
                  >
                    <Image className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => applyFormat('heading')}
                    className="p-2 rounded hover:bg-gray-200 text-gray-600 transition-colors"
                    title="标题"
                  >
                    <Type className="h-4 w-4" />
                  </button>
                </div>

                {/* 分隔线 */}
                <div className="w-px h-6 bg-gray-300 mr-4"></div>

                {/* 对齐方式 */}
                <div className="flex items-center space-x-1">
                  <button
                    onClick={() => setEditorToolbar(prev => ({ ...prev, textAlign: 'left' }))}
                    className={`p-2 rounded hover:bg-gray-200 transition-colors ${
                      editorToolbar.textAlign === 'left' ? 'bg-gray-200 text-blue-600' : 'text-gray-600'
                    }`}
                    title="左对齐"
                  >
                    <AlignLeft className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => setEditorToolbar(prev => ({ ...prev, textAlign: 'center' }))}
                    className={`p-2 rounded hover:bg-gray-200 transition-colors ${
                      editorToolbar.textAlign === 'center' ? 'bg-gray-200 text-blue-600' : 'text-gray-600'
                    }`}
                    title="居中对齐"
                  >
                    <AlignCenter className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => setEditorToolbar(prev => ({ ...prev, textAlign: 'right' }))}
                    className={`p-2 rounded hover:bg-gray-200 transition-colors ${
                      editorToolbar.textAlign === 'right' ? 'bg-gray-200 text-blue-600' : 'text-gray-600'
                    }`}
                    title="右对齐"
                  >
                    <AlignRight className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          )}
          
          {/* 编辑器内容 */}
          <div className="flex-1 p-6 overflow-y-auto">
            {currentSection ? (
              <div className="h-full flex flex-col">
                {/* 生成状态指示器 */}
                {isGeneratingContent && (
                  <div className="flex justify-end items-center mb-4">
                    <div className="flex items-center text-xs text-blue-600">
                      <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600 mr-1"></div>
                      生成中...
                    </div>
                  </div>
                )}

                <div className="flex-1 relative">
                  <textarea
                    value={currentSection.content}
                    onChange={(e) => updateSectionContent(e.target.value)}
                    className={`w-full h-full p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none font-mono text-sm leading-relaxed ${
                      editorToolbar.textAlign === 'center' ? 'text-center' :
                      editorToolbar.textAlign === 'right' ? 'text-right' : 'text-left'
                    }`}
                    placeholder="在此输入章节内容...

支持 Markdown 格式：
• **粗体文本**
• *斜体文本*
• [链接文本](URL)
• > 引用文本
• • 无序列表
• 1. 有序列表

快捷键：
• Ctrl+B 加粗
• Ctrl+I 斜体
• Ctrl+U 下划线
• Ctrl+K 插入链接
• Ctrl+S 保存文档"
                    style={{
                      fontWeight: editorToolbar.bold ? 'bold' : 'normal',
                      fontStyle: editorToolbar.italic ? 'italic' : 'normal',
                      textDecoration: editorToolbar.underline ? 'underline' : 'none'
                    }}
                  />

                  {/* 字数统计 */}
                  <div className="absolute bottom-2 right-2">
                    <div className="text-xs text-gray-400 bg-white px-2 py-1 rounded shadow">
                      {currentSection.content.length} 字符
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="flex items-center justify-center h-full text-gray-500">
                <div className="text-center">
                  <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>请从左侧目录选择要编辑的章节</p>
                  <p className="text-sm mt-2">选择章节后即可使用富文本编辑工具</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 右侧边栏 */}
        <div className="w-80 bg-white border-l border-gray-200 flex flex-col">
          {/* Tab 导航 */}
          <div className="border-b border-gray-200">
            <nav className="flex">
              <button
                onClick={() => setRightSidebarTab('references')}
                className={`flex-1 flex items-center justify-center px-4 py-3 text-sm font-medium border-b-2 transition-colors ${
                  rightSidebarTab === 'references'
                    ? 'border-blue-500 text-blue-600 bg-blue-50'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                }`}
              >
                <BookOpen className="h-4 w-4 mr-2" />
                参考资料
              </button>
              <button
                onClick={() => setRightSidebarTab('chat')}
                className={`flex-1 flex items-center justify-center px-4 py-3 text-sm font-medium border-b-2 transition-colors ${
                  rightSidebarTab === 'chat'
                    ? 'border-blue-500 text-blue-600 bg-blue-50'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                }`}
              >
                <MessageSquare className="h-4 w-4 mr-2" />
                AI助手
              </button>
            </nav>
          </div>


          {/* Tab 内容 */}
          {rightSidebarTab === 'references' ? (
            // 参考资料内容
            <>
              <div className="p-4 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium text-gray-900">当前章节参考资料</h3>
                  <span className="text-xs text-gray-500">4 个资料</span>
                </div>
              </div>

              <div className="flex-1 overflow-y-auto p-4">
                <div className="space-y-3">
                  {/* 参考资料卡片1 - 文本类型 */}
                  <div className="rounded-lg p-4 bg-white border border-gray-200 hover:shadow-md transition-shadow cursor-pointer">
                    {/* 顶部：图标 + 标签 */}
                    <div className="flex items-center gap-2 mb-3">
                      <FileText className="h-4 w-4 text-blue-500" />
                      <span className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded">
                        技术规范
                      </span>
                      <span className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">
                        第1页
                      </span>
                    </div>

                    {/* 标题 */}
                    <h4 className="text-sm font-medium text-gray-900 mb-2">环境影响评价技术导则</h4>

                    {/* 正文内容 */}
                    <div className="text-sm text-gray-600 mb-3">
                      建设项目环境影响评价应当遵循以下原则：依法评价、科学评价、突出重点、注重实效。评价工作应当客观、公正、真实、准确，为环境管理决策提供科学依据。
                    </div>

                    {/* 标签 */}
                    <div className="flex flex-wrap gap-1 mb-3">
                      <span className="text-xs px-2 py-1 bg-blue-100 text-blue-700 rounded-full">环评</span>
                      <span className="text-xs px-2 py-1 bg-blue-100 text-blue-700 rounded-full">技术导则</span>
                      <span className="text-xs px-2 py-1 bg-blue-100 text-blue-700 rounded-full">评价原则</span>
                    </div>

                    {/* 底部信息 */}
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span>来源: 环评技术导则.pdf</span>
                      <span>置信度: 95%</span>
                    </div>
                  </div>

                  {/* 参考资料卡片2 - 表格类型 */}
                  <div className="rounded-lg p-4 bg-white border border-gray-200 hover:shadow-md transition-shadow cursor-pointer">
                    {/* 顶部：图标 + 标签 */}
                    <div className="flex items-center gap-2 mb-3">
                      <Table className="h-4 w-4 text-green-500" />
                      <span className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded">
                        监测数据
                      </span>
                      <span className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">
                        第15页
                      </span>
                    </div>

                    {/* 标题 */}
                    <h4 className="text-sm font-medium text-gray-900 mb-2">环境质量现状监测数据</h4>

                    {/* 正文内容 - 表格 */}
                    <div className="text-sm text-gray-600 mb-3">
                      <div className="overflow-x-auto">
                        <table className="w-full text-xs border-collapse border border-gray-300">
                          <tr>
                            <td className="border border-gray-300 px-2 py-1 font-medium">污染物</td>
                            <td className="border border-gray-300 px-2 py-1 font-medium">浓度(μg/m³)</td>
                            <td className="border border-gray-300 px-2 py-1 font-medium">标准值</td>
                          </tr>
                          <tr>
                            <td className="border border-gray-300 px-2 py-1">PM2.5</td>
                            <td className="border border-gray-300 px-2 py-1">35</td>
                            <td className="border border-gray-300 px-2 py-1">75</td>
                          </tr>
                          <tr>
                            <td className="border border-gray-300 px-2 py-1">PM10</td>
                            <td className="border border-gray-300 px-2 py-1">68</td>
                            <td className="border border-gray-300 px-2 py-1">150</td>
                          </tr>
                          <tr>
                            <td className="border border-gray-300 px-2 py-1">SO2</td>
                            <td className="border border-gray-300 px-2 py-1">15</td>
                            <td className="border border-gray-300 px-2 py-1">150</td>
                          </tr>
                        </table>
                      </div>
                    </div>

                    {/* 标签 */}
                    <div className="flex flex-wrap gap-1 mb-3">
                      <span className="text-xs px-2 py-1 bg-blue-100 text-blue-700 rounded-full">监测数据</span>
                      <span className="text-xs px-2 py-1 bg-blue-100 text-blue-700 rounded-full">环境质量</span>
                      <span className="text-xs px-2 py-1 bg-blue-100 text-blue-700 rounded-full">大气污染</span>
                    </div>

                    {/* 底部信息 */}
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span>来源: 监测报告.xlsx</span>
                      <span>置信度: 82%</span>
                    </div>
                  </div>

                  {/* 参考资料卡片3 - 图表类型 */}
                  <div className="rounded-lg p-4 bg-white border border-gray-200 hover:shadow-md transition-shadow cursor-pointer">
                    {/* 顶部：图标 + 标签 */}
                    <div className="flex items-center gap-2 mb-3">
                      <BarChart3 className="h-4 w-4 text-purple-500" />
                      <span className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded">
                        地理信息
                      </span>
                      <span className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">
                        第5页
                      </span>
                    </div>

                    {/* 标题 */}
                    <h4 className="text-sm font-medium text-gray-900 mb-2">项目地理位置分析图</h4>

                    {/* 正文内容 */}
                    <div className="text-sm text-gray-600 mb-3">
                      项目位于XX市XX区，东临XX路，西接XX河，南靠XX工业园，北面为居民区。项目选址地理位置优越，交通便利，周边环境相对较好，距离最近的敏感点约500米。
                    </div>

                    {/* 标签 */}
                    <div className="flex flex-wrap gap-1 mb-3">
                      <span className="text-xs px-2 py-1 bg-blue-100 text-blue-700 rounded-full">地理位置</span>
                      <span className="text-xs px-2 py-1 bg-blue-100 text-blue-700 rounded-full">项目选址</span>
                      <span className="text-xs px-2 py-1 bg-blue-100 text-blue-700 rounded-full">周边环境</span>
                    </div>

                    {/* 底部信息 */}
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span>来源: 地理位置图.jpg</span>
                      <span>置信度: 76%</span>
                    </div>
                  </div>

                  {/* 参考资料卡片4 - 项目资料 */}
                  <div className="rounded-lg p-4 bg-white border border-gray-200 hover:shadow-md transition-shadow cursor-pointer">
                    {/* 顶部：图标 + 标签 */}
                    <div className="flex items-center gap-2 mb-3">
                      <FileText className="h-4 w-4 text-blue-500" />
                      <span className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded">
                        项目资料
                      </span>
                      <span className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">
                        第3页
                      </span>
                    </div>

                    {/* 标题 */}
                    <h4 className="text-sm font-medium text-gray-900 mb-2">项目基本信息表</h4>

                    {/* 正文内容 */}
                    <div className="text-sm text-gray-600 mb-3">
                      项目名称：某工业园区建设项目<br/>
                      建设单位：XX环保科技有限公司<br/>
                      建设地点：XX市XX区XX街道<br/>
                      投资总额：5000万元<br/>
                      建设性质：新建
                    </div>

                    {/* 标签 */}
                    <div className="flex flex-wrap gap-1 mb-3">
                      <span className="text-xs px-2 py-1 bg-blue-100 text-blue-700 rounded-full">项目信息</span>
                      <span className="text-xs px-2 py-1 bg-blue-100 text-blue-700 rounded-full">基本资料</span>
                      <span className="text-xs px-2 py-1 bg-blue-100 text-blue-700 rounded-full">申报材料</span>
                    </div>

                    {/* 底部信息 */}
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span>来源: 项目申报材料.docx</span>
                      <span>置信度: 88%</span>
                    </div>
                  </div>
                </div>
              </div>
            </>
          ) : (
            // AI助手聊天内容
            <>


              {/* 聊天消息区域 */}
              <div className="flex-1 overflow-y-auto p-4 space-y-4">
                {chatMessages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div
                      className={`w-full rounded-lg px-3 py-2 ${
                        message.type === 'user'
                          ? 'bg-blue-600 text-white'
                          : 'bg-gray-100 text-gray-900'
                      }`}
                    >
                      <div className="flex items-start space-x-2">
                        {message.type === 'assistant' && (
                          <Bot className="h-4 w-4 mt-0.5 flex-shrink-0 text-blue-500" />
                        )}
                        <div className="flex-1">
                          <p className="text-sm whitespace-pre-wrap">{message.content}</p>

                          {/* 显示源卡片 - 使用process-step3样式 */}
                          {message.sourceCards && message.sourceCards.length > 0 && (
                            <div className="mt-3 space-y-3">
                              <div className="text-xs text-gray-500 font-medium">参考资料 ({message.sourceCards.length}个):</div>
                              {message.sourceCards.map((card) => (
                                <div
                                  key={card.id}
                                  className="rounded-lg p-4 bg-white border border-gray-200 hover:shadow-md transition-shadow cursor-pointer"
                                  onClick={() => {
                                    if (!selectedCards.includes(card.id)) {
                                      setSelectedCards(prev => [...prev, card.id]);
                                    }
                                  }}
                                >
                                  {/* 顶部：图标 + 标签 */}
                                  <div className="flex items-center gap-2 mb-3">
                                    {card.type === 'text' && <FileText className="h-4 w-4 text-blue-500" />}
                                    {card.type === 'table' && <Table className="h-4 w-4 text-green-500" />}
                                    {card.type === 'chart' && <BarChart3 className="h-4 w-4 text-purple-500" />}
                                    {card.type === 'formula' && <Tag className="h-4 w-4 text-orange-500" />}
                                    <span className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded">
                                      {card.category}
                                    </span>
                                    <span className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">
                                      第{card.sourcePageNumber}页
                                    </span>
                                  </div>

                                  {/* 标题 */}
                                  <h4 className="text-sm font-medium text-gray-900 mb-2">{card.title}</h4>

                                  {/* 正文内容 */}
                                  <div className="text-sm text-gray-600 mb-3">
                                    {card.type === 'table' ? (
                                      <div className="overflow-x-auto">
                                        <table className="w-full text-xs border-collapse border border-gray-300">
                                          {card.content.split('\n').map((row, index) => (
                                            <tr key={index}>
                                              {row.split(' | ').map((cell, cellIndex) => (
                                                <td key={cellIndex} className="border border-gray-300 px-2 py-1">
                                                  {cell}
                                                </td>
                                              ))}
                                            </tr>
                                          ))}
                                        </table>
                                      </div>
                                    ) : (
                                      <div className="line-clamp-3">{card.content}</div>
                                    )}
                                  </div>

                                  {/* 标签 */}
                                  <div className="flex flex-wrap gap-1 mb-3">
                                    {card.tags.map((tag, index) => (
                                      <span
                                        key={index}
                                        className="text-xs px-2 py-1 bg-blue-100 text-blue-700 rounded-full"
                                      >
                                        {tag}
                                      </span>
                                    ))}
                                  </div>

                                  {/* 底部信息 */}
                                  <div className="flex items-center justify-between text-xs text-gray-500">
                                    <span>来源: {card.source}</span>
                                    <span>置信度: {Math.round(card.confidence * 100)}%</span>
                                  </div>
                                </div>
                              ))}
                              <div className="text-xs text-gray-400 italic">
                                💡 点击卡片可添加到选中列表，用于生成新内容
                              </div>
                            </div>
                          )}

                          <p className={`text-xs mt-1 ${
                            message.type === 'user' ? 'text-blue-100' : 'text-gray-500'
                          }`}>
                            {message.timestamp.toLocaleTimeString('zh-CN', {
                              hour: '2-digit',
                              minute: '2-digit'
                            })}
                          </p>
                        </div>
                        {message.type === 'user' && (
                          <User className="h-4 w-4 mt-0.5 flex-shrink-0 text-blue-100" />
                        )}
                      </div>
                    </div>
                  </div>
                ))}

                {/* AI 正在输入指示器 */}
                {isTyping && (
                  <div className="flex justify-start">
                    <div className="bg-gray-100 rounded-lg px-3 py-2">
                      <div className="flex items-center space-x-2">
                        <Bot className="h-4 w-4 text-blue-500" />
                        <div className="flex space-x-1">
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                <div ref={chatEndRef} />
              </div>
          
              {/* 聊天输入区域 */}
              <div className="p-4 border-t border-gray-200">
                <div className="flex space-x-2">
                  <input
                    type="text"
                    value={chatInput}
                    onChange={(e) => setChatInput(e.target.value)}
                    onKeyDown={(e) => e.key === 'Enter' && sendMessage()}
                    placeholder="输入消息..."
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                  <button
                    onClick={() => sendMessage()}
                    disabled={!chatInput.trim()}
                    className="px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <Send className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default DocumentEditor;
